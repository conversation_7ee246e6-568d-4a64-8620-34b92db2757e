{"name": "chatTest", "version": "1.0.0", "description": "uniapp聊天应用", "main": "main.uts", "scripts": {"build:app": "uni build --platform app", "build:app-android": "uni build --platform app-android", "build:app-ios": "uni build --platform app-ios", "build:h5": "uni build --platform h5", "build:mp-weixin": "uni build --platform mp-weixin", "build:mp-alipay": "uni build --platform mp-alipay", "build:mp-baidu": "uni build --platform mp-baidu", "build:mp-toutiao": "uni build --platform mp-toutiao", "dev:app": "uni --platform app", "dev:app-android": "uni --platform app-android", "dev:app-ios": "uni --platform app-ios", "dev:h5": "uni --platform h5", "dev:mp-weixin": "uni --platform mp-weixin", "dev:mp-alipay": "uni --platform mp-alipay", "dev:mp-baidu": "uni --platform mp-baidu", "dev:mp-toutiao": "uni --platform mp-toutiao", "info": "uni info"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4020920240930001", "@dcloudio/uni-app-plus": "3.0.0-4020920240930001", "@dcloudio/uni-h5": "3.0.0-4020920240930001", "@dcloudio/uni-mp-alipay": "3.0.0-4020920240930001", "@dcloudio/uni-mp-baidu": "3.0.0-4020920240930001", "@dcloudio/uni-mp-toutiao": "3.0.0-4020920240930001", "@dcloudio/uni-mp-weixin": "3.0.0-4020920240930001", "@dcloudio/uni-quickapp-webview": "3.0.0-4020920240930001", "vue": "^3.4.21", "vue-i18n": "^9.1.9"}, "devDependencies": {"@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4020920240930001", "@dcloudio/uni-cli-shared": "3.0.0-4020920240930001", "@dcloudio/vite-plugin-uni": "3.0.0-4020920240930001", "@vue/tsconfig": "^0.5.1", "typescript": "^5.4.5", "vite": "5.2.8", "vue-tsc": "^2.0.6"}, "browserslist": ["Android >= 4.4", "ios >= 9"], "uni-app": {"scripts": {}}}
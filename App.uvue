<script lang="uts">
	import AuthManager from './utils/auth.uts'

  // #ifdef APP-ANDROID || APP-HARMONY
	let firstBackTime = 0
  // #endif

	export default {
		onLaunch: function () {
			console.log('App Launch')
			this.checkLoginStatus()
			this.setupRequestInterceptor()
		},
		onShow: function () {
			console.log('App Show')
			this.checkTokenExpiry()
		},
		onHide: function () {
			console.log('App Hide')
		},
		// #ifdef APP-ANDROID || APP-HARMONY
		onLastPageBackPress: function () {
			console.log('App LastPageBackPress')
			if (firstBackTime == 0) {
				uni.showToast({
					title: '再按一次退出应用',
					position: 'bottom',
				})
				firstBackTime = Date.now()
				setTimeout(() => {
					firstBackTime = 0
				}, 2000)
			} else if (Date.now() - firstBackTime < 2000) {
				firstBackTime = Date.now()
				uni.exit()
			}
		},
		// #endif
		onExit: function () {
			console.log('App Exit')
		},

		methods: {
			// 检查登录状态
			checkLoginStatus: function () {
				const isLoggedIn = AuthManager.isLoggedIn()
				console.log('Login status:', isLoggedIn)

				// 如果未登录，跳转到登录页
				if (!isLoggedIn) {
					setTimeout(() => {
						uni.reLaunch({
							url: '/pages/login/login'
						})
					}, 100)
				}
			},

			// 检查token是否即将过期
			checkTokenExpiry: function () {
				const remainingTime = AuthManager.getTokenRemainingTime()

				if (remainingTime > 0 && remainingTime < 24 * 60 * 60 * 1000) { // 少于1天时提醒
					uni.showModal({
						title: '提醒',
						content: '您的登录状态即将过期，是否需要延长？',
						success: (res) => {
							if (res.confirm) {
								AuthManager.refreshToken()
								uni.showToast({
									title: '登录状态已延长',
									icon: 'success'
								})
							}
						}
					})
				}
			},

			// 设置请求拦截器
			setupRequestInterceptor: function () {
				// 拦截uni.request
				const originalRequest = uni.request
				uni.request = function (options: any) {
					// 添加token到请求头
					options = AuthManager.addTokenToRequest(options)

					// 添加响应处理
					const originalSuccess = options.success
					options.success = function (res: any) {
						// 处理token过期等情况
						AuthManager.handleApiResponse(res)
						if (originalSuccess) {
							originalSuccess(res)
						}
					}

					return originalRequest.call(this, options)
				}
			}
		}
	}
</script>

<style>
	/*每个页面公共css */
	.uni-row {
		flex-direction: row;
	}

	.uni-column {
		flex-direction: column;
	}
</style>
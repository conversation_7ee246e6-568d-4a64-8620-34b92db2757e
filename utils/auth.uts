/**
 * 认证工具类
 * 用于管理用户登录状态、token存储和权限验证
 */

export class AuthManager {
	private static TOKEN_KEY = 'user_token'
	private static LOGIN_TIME_KEY = 'login_time'
	private static TOKEN_EXPIRE_TIME = 7 * 24 * 60 * 60 * 1000 // 7天过期

	/**
	 * 保存用户token
	 * @param token 用户token
	 */
	static saveToken(token: string): void {
		const loginTime = Date.now()
		uni.setStorageSync(this.TOKEN_KEY, token)
		uni.setStorageSync(this.LOGIN_TIME_KEY, loginTime)
	}

	/**
	 * 获取用户token
	 * @returns token字符串或null
	 */
	static getToken(): string | null {
		const token = uni.getStorageSync(this.TOKEN_KEY)
		const loginTime = uni.getStorageSync(this.LOGIN_TIME_KEY)

		if (!token || !loginTime) {
			return null
		}

		// 检查token是否过期
		const currentTime = Date.now()
		if (currentTime - loginTime > this.TOKEN_EXPIRE_TIME) {
			this.clearToken()
			return null
		}

		return token
	}

	/**
	 * 清除用户token
	 */
	static clearToken(): void {
		uni.removeStorageSync(this.TOKEN_KEY)
		uni.removeStorageSync(this.LOGIN_TIME_KEY)
	}

	/**
	 * 检查用户是否已登录
	 * @returns 是否已登录
	 */
	static isLoggedIn(): boolean {
		return this.getToken() !== null
	}

	/**
	 * 刷新token过期时间
	 */
	static refreshToken(): void {
		const token = this.getToken()
		if (token) {
			const newLoginTime = Date.now()
			uni.setStorageSync(this.LOGIN_TIME_KEY, newLoginTime)
		}
	}

	/**
	 * 获取token剩余有效时间（毫秒）
	 * @returns 剩余时间或-1（无效token）
	 */
	static getTokenRemainingTime(): number {
		const loginTime = uni.getStorageSync(this.LOGIN_TIME_KEY)
		if (!loginTime) {
			return -1
		}

		const currentTime = Date.now()
		const remainingTime = this.TOKEN_EXPIRE_TIME - (currentTime - loginTime)
		return remainingTime > 0 ? remainingTime : -1
	}

	/**
	 * 登出
	 */
	static logout(): void {
		this.clearToken()

		// 清除其他用户相关数据
		uni.removeStorageSync('remembered_username')
		uni.removeStorageSync('user_info')

		// 跳转到登录页
		uni.reLaunch({
			url: '/pages/login/login'
		})

		uni.showToast({
			title: '已退出登录',
			icon: 'success'
		})
	}

	/**
	 * 检查页面访问权限
	 * @param pagePath 页面路径
	 * @returns 是否有权限访问
	 */
	static checkPagePermission(pagePath: string): boolean {
		// 定义不需要登录的页面
		const publicPages = [
			'/pages/login/login',
			'/pages/register/register'
		]

		// 如果是公开页面，直接允许访问
		if (publicPages.includes(pagePath)) {
			return true
		}

		// 其他页面需要登录
		return this.isLoggedIn()
	}

	/**
	 * 页面访问守卫
	 * 如果用户未登录且访问需要权限的页面，自动跳转到登录页
	 * @param pagePath 当前页面路径
	 */
	static pageGuard(pagePath: string): void {
		if (!this.checkPagePermission(pagePath)) {
			uni.showToast({
				title: '请先登录',
				icon: 'error'
			})

			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/login/login'
				})
			}, 1500)
		}
	}

	/**
	 * API请求拦截器 - 自动添加token
	 * @param options 请求参数
	 * @returns 处理后的请求参数
	 */
	static addTokenToRequest(options: any): any {
		const token = this.getToken()
		if (token) {
			options.header = options.header || {}
			options.header['Authorization'] = `Bearer ${token}`
		}
		return options
	}

	/**
	 * API响应拦截器 - 处理token过期
	 * @param response 响应数据
	 */
	static handleApiResponse(response: any): void {
		// 如果返回401或token过期，清除本地token并跳转登录
		if (response.statusCode === 401 ||
			(response.data && response.data.code === 'TOKEN_EXPIRED')) {
			this.logout()
		}
	}
}

// 全局导出认证管理器实例
export default AuthManager
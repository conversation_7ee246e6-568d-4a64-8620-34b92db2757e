<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="page-container">
			<u-navbar
				:placeholder="true"
				bg-color="linear-gradient(45deg, #667eea, #764ba2)"
				:auto-back="false"
			>
				<view slot="left">
					<u-text text="通讯录" size="18" bold color="#ffffff"></u-text>
				</view>
				<view slot="right" @click="handleLogout">
					<u-button
						text="退出"
						size="mini"
						type="info"
						plain
						color="#ffffff"
						border-color="#ffffff"
					></u-button>
				</view>
			</u-navbar>

			<view class="content">
				<u-empty
					mode="list"
					text="通讯录功能即将上线"
					:show="true"
				>
					<view slot="bottom">
						<u-gap height="20"></u-gap>
						<u-text
							text="欢迎来到聊天助手通讯录"
							size="16"
							color="#333"
							align="center"
						></u-text>
						<u-gap height="10"></u-gap>
						<u-text
							text="这里需要登录后才能访问"
							size="14"
							color="#999"
							align="center"
						></u-text>
					</view>
				</u-empty>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { onMounted } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	// 页面加载时检查登录状态
	onMounted(() => {
		const currentPage = '/pages/addressBook/addressBook'
		AuthManager.pageGuard(currentPage)
	})

	// 退出登录
	const handleLogout = () => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					AuthManager.logout()
				}
			}
		})
	}
</script>

<style>
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.content {
		padding: 40px 20px;
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: calc(100vh - 44px);
	}
</style>

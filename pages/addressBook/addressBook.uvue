<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="page-container">
			<view class="navbar">
				<text class="nav-title">通讯录</text>
				<button class="logout-btn" @click="handleLogout">退出</button>
			</view>

			<view class="content">
				<view class="empty-state">
					<text class="empty-icon">📝</text>
					<text class="empty-title">通讯录功能即将上线</text>
					<text class="empty-subtitle">欢迎来到聊天助手通讯录</text>
					<text class="empty-description">这里需要登录后才能访问</text>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { onMounted } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	// 页面加载时检查登录状态
	onMounted(() => {
		const currentPage = '/pages/addressBook/addressBook'
		AuthManager.pageGuard(currentPage)
	})

	// 退出登录
	const handleLogout = () => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					AuthManager.logout()
				}
			}
		})
	}
</script>

<style>
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.navbar {
		height: 44px;
		background: linear-gradient(45deg, #667eea, #764ba2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20px;
		padding-top: var(--status-bar-height, 0);
	}

	.nav-title {
		color: #ffffff;
		font-size: 18px;
		font-weight: bold;
	}

	.logout-btn {
		background: transparent;
		border: 1px solid #ffffff;
		color: #ffffff;
		padding: 6px 12px;
		border-radius: 15px;
		font-size: 12px;
	}

	.content {
		padding: 40px 20px;
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: calc(100vh - 44px);
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		text-align: center;
		padding: 60px 30px;
		background: #ffffff;
		border-radius: 16px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		max-width: 300px;
	}

	.empty-icon {
		font-size: 64px;
		margin-bottom: 20px;
	}

	.empty-title {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		margin-bottom: 10px;
	}

	.empty-subtitle {
		font-size: 16px;
		color: #333;
		margin-bottom: 10px;
	}

	.empty-description {
		font-size: 14px;
		color: #999;
	}
</style>

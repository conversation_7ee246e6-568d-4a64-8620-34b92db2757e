<template>
	<view class="page-container">
		<u-navbar
			:placeholder="true"
			bg-color="linear-gradient(45deg, #667eea, #764ba2)"
			:auto-back="false"
		>
			<view slot="left">
				<u-text text="聊天助手" size="18" bold color="#ffffff"></u-text>
			</view>
			<view slot="right" @click="handleLogout">
				<u-button
					text="退出"
					size="mini"
					type="info"
					plain
					color="#ffffff"
					border-color="#ffffff"
				></u-button>
			</view>
		</u-navbar>

		<view class="main-content">
			<u-image
				src="/static/logo.png"
				width="100px"
				height="100px"
				mode="aspectFit"
			></u-image>

			<u-gap height="20"></u-gap>

			<u-text
				:text="title"
				size="24"
				bold
				color="#333"
			></u-text>

			<u-gap height="15"></u-gap>

			<u-text
				text="欢迎使用聊天助手！"
				size="16"
				color="#666"
			></u-text>

			<u-gap height="30"></u-gap>

			<u-card
				:show-head="false"
				:show-foot="false"
				margin="20px"
				padding="20px"
				border-radius="12"
			>
				<view class="user-info">
					<u-cell-group :border="false">
						<u-cell title="登录状态" value="已登录" :arrow="false">
							<u-icon slot="icon" name="checkmark-circle" color="#19be6b" size="20"></u-icon>
						</u-cell>
						<u-cell :title="`Token有效期`" :value="formatRemainingTime" :arrow="false">
							<u-icon slot="icon" name="time" color="#ff9900" size="20"></u-icon>
						</u-cell>
					</u-cell-group>
				</view>
			</u-card>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	const title = ref("聊天助手首页")
	const remainingTime = ref(0)

	// 格式化剩余时间显示
	const formatRemainingTime = computed(() => {
		if (remainingTime.value <= 0) return '已过期'

		const days = Math.floor(remainingTime.value / (1000 * 60 * 60 * 24))
		const hours = Math.floor((remainingTime.value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

		if (days > 0) {
			return `${days}天${hours}小时`
		} else if (hours > 0) {
			return `${hours}小时`
		} else {
			const minutes = Math.floor((remainingTime.value % (1000 * 60 * 60)) / (1000 * 60))
			return `${minutes}分钟`
		}
	})

	// 页面加载时检查登录状态
	onMounted(() => {
		const currentPage = '/pages/index/index'
		AuthManager.pageGuard(currentPage)

		// 获取token剩余时间
		remainingTime.value = AuthManager.getTokenRemainingTime()
	})

	// 退出登录
	const handleLogout = () => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					AuthManager.logout()
				}
			}
		})
	}
</script>

<style>
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.main-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40px 20px;
	}

	.user-info {
		width: 100%;
	}
</style>

<template>
	<view class="page-container">
		<view class="navbar">
			<text class="nav-title">聊天助手</text>
			<button class="logout-btn" @click="handleLogout">退出</button>
		</view>

		<view class="main-content">
			<image
				src="/static/logo.png"
				class="logo"
				mode="aspectFit"
			></image>

			<text class="page-title">{{ title }}</text>
			<text class="welcome-text">欢迎使用聊天助手！</text>

			<view class="info-card">
				<view class="user-info">
					<view class="info-item">
						<text class="info-icon">✓</text>
						<view class="info-content">
							<text class="info-title">登录状态</text>
							<text class="info-value">已登录</text>
						</view>
					</view>
					<view class="info-item">
						<text class="info-icon time-icon">⏰</text>
						<view class="info-content">
							<text class="info-title">Token有效期</text>
							<text class="info-value">{{ formatRemainingTime }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed, onMounted } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	const title = ref("聊天助手首页")
	const remainingTime = ref(0)

	// 格式化剩余时间显示
	const formatRemainingTime = computed(() => {
		if (remainingTime.value <= 0) return '已过期'

		const days = Math.floor(remainingTime.value / (1000 * 60 * 60 * 24))
		const hours = Math.floor((remainingTime.value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))

		if (days > 0) {
			return `${days}天${hours}小时`
		} else if (hours > 0) {
			return `${hours}小时`
		} else {
			const minutes = Math.floor((remainingTime.value % (1000 * 60 * 60)) / (1000 * 60))
			return `${minutes}分钟`
		}
	})

	// 页面加载时检查登录状态
	onMounted(() => {
		const currentPage = '/pages/index/index'
		AuthManager.pageGuard(currentPage)

		// 获取token剩余时间
		remainingTime.value = AuthManager.getTokenRemainingTime()
	})

	// 退出登录
	const handleLogout = () => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					AuthManager.logout()
				}
			}
		})
	}
</script>

<style>
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.navbar {
		height: 44px;
		background: linear-gradient(45deg, #667eea, #764ba2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20px;
		padding-top: var(--status-bar-height, 0);
	}

	.nav-title {
		color: #ffffff;
		font-size: 18px;
		font-weight: bold;
	}

	.logout-btn {
		background: transparent;
		border: 1px solid #ffffff;
		color: #ffffff;
		padding: 6px 12px;
		border-radius: 15px;
		font-size: 12px;
	}

	.main-content {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 40px 20px;
	}

	.logo {
		width: 100px;
		height: 100px;
		margin-bottom: 20px;
	}

	.page-title {
		font-size: 24px;
		font-weight: bold;
		color: #333;
		margin-bottom: 15px;
	}

	.welcome-text {
		font-size: 16px;
		color: #666;
		margin-bottom: 30px;
	}

	.info-card {
		width: 100%;
		max-width: 400px;
		background: #ffffff;
		border-radius: 12px;
		padding: 20px;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	}

	.user-info {
		width: 100%;
	}

	.info-item {
		display: flex;
		align-items: center;
		padding: 15px 0;
		border-bottom: 1px solid #f0f0f0;
	}

	.info-item:last-child {
		border-bottom: none;
	}

	.info-icon {
		width: 24px;
		height: 24px;
		border-radius: 50%;
		background: #19be6b;
		color: #ffffff;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 14px;
		margin-right: 15px;
	}

	.time-icon {
		background: #ff9900;
	}

	.info-content {
		flex: 1;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.info-title {
		color: #333;
		font-size: 16px;
	}

	.info-value {
		color: #666;
		font-size: 14px;
	}
</style>

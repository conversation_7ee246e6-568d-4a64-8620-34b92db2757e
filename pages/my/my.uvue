<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="page-container">
			<view class="navbar">
				<text class="nav-title">我的</text>
				<button class="logout-btn" @click="handleLogout">退出</button>
			</view>

			<view class="content">
				<view class="user-card">
					<view class="user-avatar">
						<text class="avatar-text">用</text>
					</view>
					<view class="user-info">
						<text class="username">聊天用户</text>
						<text class="user-desc">欢迎使用聊天助手</text>
					</view>
				</view>

				<view class="menu-list">
					<view class="menu-item" @click="showComingSoon">
						<text class="menu-icon">⚙️</text>
						<text class="menu-title">设置</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="showComingSoon">
						<text class="menu-icon">📞</text>
						<text class="menu-title">联系我们</text>
						<text class="menu-arrow">></text>
					</view>
					<view class="menu-item" @click="showComingSoon">
						<text class="menu-icon">ℹ️</text>
						<text class="menu-title">关于我们</text>
						<text class="menu-arrow">></text>
					</view>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { onMounted } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	// 页面加载时检查登录状态
	onMounted(() => {
		const currentPage = '/pages/my/my'
		AuthManager.pageGuard(currentPage)
	})

	// 退出登录
	const handleLogout = () => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					AuthManager.logout()
				}
			}
		})
	}

	// 显示即将上线提示
	const showComingSoon = () => {
		uni.showToast({
			title: '功能即将上线',
			icon: 'none'
		})
	}
</script>

<style>
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.navbar {
		height: 44px;
		background: linear-gradient(45deg, #667eea, #764ba2);
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 20px;
		padding-top: var(--status-bar-height, 0);
	}

	.nav-title {
		color: #ffffff;
		font-size: 18px;
		font-weight: bold;
	}

	.logout-btn {
		background: transparent;
		border: 1px solid #ffffff;
		color: #ffffff;
		padding: 6px 12px;
		border-radius: 15px;
		font-size: 12px;
	}

	.content {
		padding: 20px;
	}

	.user-card {
		background: #ffffff;
		border-radius: 16px;
		padding: 20px;
		margin-bottom: 20px;
		display: flex;
		align-items: center;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	}

	.user-avatar {
		width: 60px;
		height: 60px;
		border-radius: 30px;
		background: linear-gradient(45deg, #667eea, #764ba2);
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 15px;
	}

	.avatar-text {
		color: #ffffff;
		font-size: 24px;
		font-weight: bold;
	}

	.user-info {
		flex: 1;
	}

	.username {
		font-size: 18px;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 5px;
	}

	.user-desc {
		font-size: 14px;
		color: #999;
		display: block;
	}

	.menu-list {
		background: #ffffff;
		border-radius: 16px;
		overflow: hidden;
		box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
	}

	.menu-item {
		display: flex;
		align-items: center;
		padding: 15px 20px;
		border-bottom: 1px solid #f0f0f0;
		transition: background-color 0.3s;
	}

	.menu-item:last-child {
		border-bottom: none;
	}

	.menu-item:active {
		background-color: #f8f9fa;
	}

	.menu-icon {
		font-size: 20px;
		margin-right: 15px;
	}

	.menu-title {
		flex: 1;
		font-size: 16px;
		color: #333;
	}

	.menu-arrow {
		font-size: 16px;
		color: #999;
	}
</style>

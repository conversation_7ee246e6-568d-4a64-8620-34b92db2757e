<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="register-container">
			<view class="register-header">
				<u-icon
					name="arrow-left"
					color="#ffffff"
					size="24"
					@click="goBack"
					class="back-btn"
				></u-icon>
				<u-text
					text="注册账号"
					size="24"
					bold
					color="#ffffff"
				></u-text>
			</view>

			<u-card
				:show-head="false"
				:show-foot="false"
				margin="20px"
				padding="30px"
				border-radius="20"
			>
				<view class="register-form">
					<u-form :model="registerForm" ref="formRef">
						<u-form-item
							:border-bottom="false"
							:error-message="errors.username"
							:show-error="!!errors.username"
						>
							<u-input
								v-model="registerForm.username"
								placeholder="请输入用户名"
								border="surround"
								:clearable="true"
								:error="!!errors.username"
							></u-input>
						</u-form-item>

						<u-gap height="20"></u-gap>

						<u-form-item
							:border-bottom="false"
							:error-message="errors.password"
							:show-error="!!errors.password"
						>
							<u-input
								v-model="registerForm.password"
								type="password"
								placeholder="请输入密码"
								border="surround"
								:clearable="true"
								:error="!!errors.password"
							></u-input>
						</u-form-item>

						<u-gap height="20"></u-gap>

						<u-form-item
							:border-bottom="false"
							:error-message="errors.confirmPassword"
							:show-error="!!errors.confirmPassword"
						>
							<u-input
								v-model="registerForm.confirmPassword"
								type="password"
								placeholder="请确认密码"
								border="surround"
								:clearable="true"
								:error="!!errors.confirmPassword"
							></u-input>
						</u-form-item>

						<u-gap height="30"></u-gap>

						<u-button
							:text="isLoading ? '注册中...' : '注册'"
							type="primary"
							:loading="isLoading"
							:disabled="isLoading"
							@click="handleRegister"
							shape="circle"
							size="large"
						></u-button>

						<u-gap height="20"></u-gap>

						<view class="login-link">
							<u-text text="已有账号？" size="14" color="#999"></u-text>
							<u-text
								text="立即登录"
								size="14"
								color="#3c9cff"
								@click="goToLogin"
								:decoration="true"
							></u-text>
						</view>
					</u-form>
				</view>
			</u-card>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, reactive } from 'vue'

	const registerForm = reactive({
		username: '',
		password: '',
		confirmPassword: ''
	})

	const errors = reactive({
		username: '',
		password: '',
		confirmPassword: ''
	})

	const isLoading = ref(false)
	const formRef = ref()

	const validateForm = () => {
		errors.username = ''
		errors.password = ''
		errors.confirmPassword = ''
		let isValid = true

		if (!registerForm.username.trim()) {
			errors.username = '请输入用户名'
			isValid = false
		} else if (registerForm.username.length < 3) {
			errors.username = '用户名至少3个字符'
			isValid = false
		}

		if (!registerForm.password.trim()) {
			errors.password = '请输入密码'
			isValid = false
		} else if (registerForm.password.length < 6) {
			errors.password = '密码至少6个字符'
			isValid = false
		}

		if (!registerForm.confirmPassword.trim()) {
			errors.confirmPassword = '请确认密码'
			isValid = false
		} else if (registerForm.password !== registerForm.confirmPassword) {
			errors.confirmPassword = '两次密码输入不一致'
			isValid = false
		}

		return isValid
	}

	const handleRegister = async () => {
		if (!validateForm()) {
			return
		}

		isLoading.value = true

		try {
			// 模拟注册API调用
			await simulateRegister(registerForm.username, registerForm.password)

			uni.showToast({
				title: '注册成功！',
				icon: 'success'
			})

			// 注册成功后跳转到登录页
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			}, 1500)

		} catch (error) {
			uni.showToast({
				title: (error as Error)?.message || '注册失败',
				icon: 'error'
			})
		} finally {
			isLoading.value = false
		}
	}

	const simulateRegister = (username, password) => {
		return new Promise((resolve, reject) => {
			setTimeout(() => {
				// 简单的模拟注册逻辑
				if (username && password) {
					resolve({ success: true })
				} else {
					reject(new Error('注册信息不完整'))
				}
			}, 1500)
		})
	}

	const goBack = () => {
		uni.navigateBack()
	}

	const goToLogin = () => {
		uni.navigateTo({
			url: '/pages/login/login'
		})
	}
</script>

<style>
	.register-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 0 0 40px 0;
	}

	.register-header {
		display: flex;
		align-items: center;
		padding: 60px 30px 40px 30px;
		position: relative;
	}

	.back-btn {
		position: absolute;
		left: 20px;
		padding: 10px;
		cursor: pointer;
	}

	.register-form {
		width: 100%;
	}

	.login-link {
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 5px;
	}
</style>

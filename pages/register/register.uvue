<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="register-container">
			<view class="register-header">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">注册账号</text>
			</view>

			<view class="register-card">
				<view class="register-form">
					<view class="form-item">
						<input
							v-model="registerForm.username"
							placeholder="请输入用户名"
							class="input"
							:class="{ 'input-error': !!errors.username }"
						/>
						<text v-if="errors.username" class="error-text">{{ errors.username }}</text>
					</view>

					<view class="form-item">
						<input
							v-model="registerForm.password"
							type="password"
							placeholder="请输入密码"
							class="input"
							:class="{ 'input-error': !!errors.password }"
						/>
						<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
					</view>

					<view class="form-item">
						<input
							v-model="registerForm.confirmPassword"
							type="password"
							placeholder="请确认密码"
							class="input"
							:class="{ 'input-error': !!errors.confirmPassword }"
						/>
						<text v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</text>
					</view>

					<button
						class="register-btn"
						:class="{ 'btn-loading': isLoading }"
						:disabled="isLoading"
						@click="handleRegister"
					>
						{{ isLoading ? '注册中...' : '注册' }}
					</button>

					<view class="login-link">
						<text class="link-text">已有账号？</text>
						<text class="link-button" @click="goToLogin">立即登录</text>
					</view>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, reactive } from 'vue'

	const registerForm = reactive({
		username: '',
		password: '',
		confirmPassword: ''
	})

	const errors = reactive({
		username: '',
		password: '',
		confirmPassword: ''
	})

	const isLoading = ref(false)
	const formRef = ref()

	const validateForm = () => {
		errors.username = ''
		errors.password = ''
		errors.confirmPassword = ''
		let isValid = true

		if (!registerForm.username.trim()) {
			errors.username = '请输入用户名'
			isValid = false
		} else if (registerForm.username.length < 3) {
			errors.username = '用户名至少3个字符'
			isValid = false
		}

		if (!registerForm.password.trim()) {
			errors.password = '请输入密码'
			isValid = false
		} else if (registerForm.password.length < 6) {
			errors.password = '密码至少6个字符'
			isValid = false
		}

		if (!registerForm.confirmPassword.trim()) {
			errors.confirmPassword = '请确认密码'
			isValid = false
		} else if (registerForm.password !== registerForm.confirmPassword) {
			errors.confirmPassword = '两次密码输入不一致'
			isValid = false
		}

		return isValid
	}

	const handleRegister = async () => {
		if (!validateForm()) {
			return
		}

		isLoading.value = true

		try {
			// 模拟注册API调用
			await simulateRegister(registerForm.username, registerForm.password)

			uni.showToast({
				title: '注册成功！',
				icon: 'success'
			})

			// 注册成功后跳转到登录页
			setTimeout(() => {
				uni.navigateTo({
					url: '/pages/login/login'
				})
			}, 1500)

		} catch (error) {
			uni.showToast({
				title: (error as Error)?.message || '注册失败',
				icon: 'error'
			})
		} finally {
			isLoading.value = false
		}
	}

	const simulateRegister = (username: string, password: string) => {
		return new Promise((resolve, reject) => {
			setTimeout(() => {
				// 简单的模拟注册逻辑
				if (username && password) {
					resolve({ success: true })
				} else {
					reject(new Error('注册信息不完整'))
				}
			}, 1500)
		})
	}

	const goBack = () => {
		uni.navigateBack()
	}

	const goToLogin = () => {
		uni.navigateTo({
			url: '/pages/login/login'
		})
	}
</script>

<style>
	.register-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 0 0 40px 0;
	}

	.register-header {
		display: flex;
		align-items: center;
		padding: 60px 30px 40px 30px;
		position: relative;
		justify-content: center;
	}

	.back-btn {
		position: absolute;
		left: 20px;
		width: 40px;
		height: 40px;
		background: rgba(255, 255, 255, 0.2);
		border-radius: 20px;
		color: #ffffff;
		font-size: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.header-title {
		color: #ffffff;
		font-size: 24px;
		font-weight: bold;
	}

	.register-card {
		margin: 0 20px;
		padding: 30px;
		background: #ffffff;
		border-radius: 20px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.register-form {
		width: 100%;
	}

	.form-item {
		margin-bottom: 20px;
	}

	.input {
		width: 100%;
		height: 50px;
		padding: 0 16px;
		border: 2px solid #e8e8e8;
		border-radius: 12px;
		font-size: 16px;
		background: #ffffff;
		box-sizing: border-box;
		transition: border-color 0.3s;
	}

	.input:focus {
		border-color: #667eea;
		outline: none;
	}

	.input-error {
		border-color: #ff4757 !important;
	}

	.error-text {
		color: #ff4757;
		font-size: 12px;
		margin-top: 5px;
		display: block;
	}

	.register-btn {
		width: 100%;
		height: 50px;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: bold;
		margin: 30px 0 20px 0;
		transition: opacity 0.3s;
	}

	.register-btn:active {
		opacity: 0.8;
	}

	.btn-loading {
		opacity: 0.7;
		pointer-events: none;
	}

	.login-link {
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 5px;
	}

	.link-text {
		color: #999;
		font-size: 14px;
	}

	.link-button {
		color: #667eea;
		font-size: 14px;
		text-decoration: underline;
	}
</style>

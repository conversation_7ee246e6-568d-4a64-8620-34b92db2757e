<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="login-container">
			<view class="login-header">
				<text class="title">聊天助手</text>
				<text class="subtitle">欢迎回来</text>
			</view>

			<view class="login-card">
				<view class="login-form">
					<view class="form-item">
						<input
							v-model="loginForm.username"
							placeholder="请输入用户名"
							class="input"
							:class="{ 'input-error': !!errors.username }"
						/>
						<text v-if="errors.username" class="error-text">{{ errors.username }}</text>
					</view>

					<view class="form-item">
						<input
							v-model="loginForm.password"
							type="password"
							placeholder="请输入密码"
							class="input"
							:class="{ 'input-error': !!errors.password }"
						/>
						<text v-if="errors.password" class="error-text">{{ errors.password }}</text>
					</view>

					<view class="form-item">
						<view class="captcha-container">
							<input
								v-model="loginForm.captcha"
								placeholder="请输入验证码"
								class="input captcha-input"
								:class="{ 'input-error': !!errors.captcha }"
							/>
							<view class="captcha-image-container" @click="refreshCaptcha">
								<image
									v-if="captchaImage"
									:src="captchaImage"
									class="captcha-image"
									mode="aspectFit"
								></image>
								<text v-else class="captcha-loading">加载中...</text>
							</view>
						</view>
						<text v-if="errors.captcha" class="error-text">{{ errors.captcha }}</text>
					</view>

					<button
						class="login-btn"
						:class="{ 'btn-loading': isLoading }"
						:disabled="isLoading"
						@click="handleLogin"
					>
						{{ isLoading ? '登录中...' : '登录' }}
					</button>

					<view class="register-link">
						<text class="link-text">还没有账号？</text>
						<text class="link-button" @click="goToRegister">立即注册</text>
					</view>
				</view>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, reactive, onMounted } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	const loginForm = reactive({
		username: '',
		password: '',
		captcha: ''
	})

	const errors = reactive({
		username: '',
		password: '',
		captcha: ''
	})

	const isLoading = ref(false)
	const formRef = ref()
	const captchaImage = ref('')
	const captchaKey = ref('')

	const validateForm = () => {
		errors.username = ''
		errors.password = ''
		errors.captcha = ''
		let isValid = true

		if (!loginForm.username.trim()) {
			errors.username = '请输入用户名'
			isValid = false
		} else if (loginForm.username.length < 3) {
			errors.username = '用户名至少3个字符'
			isValid = false
		}

		if (!loginForm.password.trim()) {
			errors.password = '请输入密码'
			isValid = false
		} else if (loginForm.password.length < 6) {
			errors.password = '密码至少6个字符'
			isValid = false
		}

		if (!loginForm.captcha.trim()) {
			errors.captcha = '请输入验证码'
			isValid = false
		} else if (loginForm.captcha.length < 4) {
			errors.captcha = '验证码不正确'
			isValid = false
		}

		return isValid
	}

	const handleLogin = async () => {
		if (!validateForm()) {
			return
		}

		isLoading.value = true

		try {
			// 模拟登录API调用，包含验证码校验
			await simulateLogin(loginForm.username, loginForm.password, loginForm.captcha, captchaKey.value)

			// 登录成功，保存token
			const token = generateToken()
			AuthManager.saveToken(token)

			uni.showToast({
				title: '登录成功',
				icon: 'success'
			})

			// 跳转到主页
			setTimeout(() => {
				uni.switchTab({
					url: '/pages/index/index'
				})
			}, 1000)

		} catch (error) {
			// 验证码错误时刷新验证码
			refreshCaptcha()
			loginForm.captcha = ''

			uni.showToast({
				title: (error as Error)?.message || '登录失败',
				icon: 'error'
			})
		} finally {
			isLoading.value = false
		}
	}

	const simulateLogin = async (username: string, password: string, captcha: string, key: string) => {
		// 先验证验证码
		const captchaValid = await verifyCaptcha(captcha, key)
		if (!captchaValid) {
			throw new Error('验证码错误')
		}

		// 然后验证用户名密码（这里还是模拟，你可以替换成真实API）
		return new Promise((resolve, reject) => {
			setTimeout(() => {
				if (username === 'admin' && password === '123456') {
					resolve({ success: true })
				} else {
					reject(new Error('用户名或密码错误'))
				}
			}, 1500)
		})
	}

	// 验证验证码的API调用
	const verifyCaptcha = async (captcha: string, imgId: string) => {
		try {
			const response = await uni.request({
				url: '/api/v1/captcha/verify',
				method: 'POST',
				header: {
					'Content-Type': 'application/json'
				},
				data: {
					imgCode: captcha,
					imgId: imgId
				}
			})

			return response.statusCode === 200 && response.data && response.data.code === 200
		} catch (error) {
			console.error('验证码校验错误:', error)
			return false
		}
	}

	const generateToken = () => {
		return 'token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
	}

	// 获取验证码
	const fetchCaptcha = async () => {
		try {
			const response = await uni.request({
				url: '/api/v1/captcha',
				method: 'GET',
				header: {
					'Content-Type': 'application/json'
				}
			})

			if (response.statusCode === 200 && response.data && response.data.code === 200) {
				captchaKey.value = response.data.data.imgId || ''
				captchaImage.value = response.data.data.imgCode || ''
			} else {
				throw new Error('获取验证码失败')
			}
		} catch (error) {
			console.error('获取验证码错误:', error)
			uni.showToast({
				title: '获取验证码失败',
				icon: 'error'
			})
		}
	}

	// 刷新验证码
	const refreshCaptcha = () => {
		captchaImage.value = ''
		fetchCaptcha()
	}

	// 页面加载时获取验证码
	onMounted(() => {
		fetchCaptcha()
	})

	const goToRegister = () => {
		uni.navigateTo({
			url: '/pages/register/register'
		})
	}
</script>

<style>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40px 0;
	}

	.login-header {
		text-align: center;
		margin-bottom: 50px;
		padding: 0 30px;
	}

	.title {
		font-size: 32px;
		font-weight: bold;
		color: #ffffff;
		display: block;
		margin-bottom: 12px;
	}

	.subtitle {
		font-size: 18px;
		color: rgba(255, 255, 255, 0.8);
		display: block;
	}

	.login-card {
		margin: 0 20px;
		padding: 30px;
		background: #ffffff;
		border-radius: 20px;
		box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
	}

	.login-form {
		width: 100%;
	}

	.form-item {
		margin-bottom: 20px;
	}

	.input {
		width: 100%;
		height: 50px;
		padding: 0 16px;
		border: 2px solid #e8e8e8;
		border-radius: 12px;
		font-size: 16px;
		background: #ffffff;
		box-sizing: border-box;
		transition: border-color 0.3s;
	}

	.captcha-container .input {
		width: auto;
		flex: 1;
	}

	.input:focus {
		border-color: #667eea;
		outline: none;
	}

	.input-error {
		border-color: #ff4757 !important;
	}

	.error-text {
		color: #ff4757;
		font-size: 12px;
		margin-top: 5px;
		display: block;
	}

	.login-btn {
		width: 100%;
		height: 50px;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #ffffff;
		border: none;
		border-radius: 25px;
		font-size: 18px;
		font-weight: bold;
		margin: 30px 0 20px 0;
		transition: opacity 0.3s;
	}

	.login-btn:active {
		opacity: 0.8;
	}

	.btn-loading {
		opacity: 0.7;
		pointer-events: none;
	}

	.register-link {
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 5px;
	}

	.link-text {
		color: #999;
		font-size: 14px;
	}

	.link-button {
		color: #667eea;
		font-size: 14px;
		text-decoration: underline;
	}

	.captcha-container {
		display: flex;
		align-items: center;
		gap: 10px;
	}


	.captcha-image-container {
		width: 120px;
		height: 50px;
		border: 2px solid #e8e8e8;
		border-radius: 8px;
		background: #f8f9fa;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		transition: border-color 0.3s;
	}

	.captcha-image-container:hover {
		border-color: #667eea;
	}

	.captcha-image {
		width: 100%;
		height: 100%;
		border-radius: 6px;
	}

	.captcha-loading {
		font-size: 12px;
		color: #999;
	}
</style>
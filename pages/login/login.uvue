<template>
	<!-- #ifdef APP -->
	<scroll-view style="flex:1">
	<!-- #endif -->
		<view class="login-container">
			<view class="login-header">
				<u-text
					text="聊天助手"
					size="32"
					bold
					color="#ffffff"
				></u-text>
				<u-gap height="12"></u-gap>
				<u-text
					text="欢迎回来"
					size="18"
					color="rgba(255, 255, 255, 0.8)"
				></u-text>
			</view>

			<u-card
				:show-head="false"
				:show-foot="false"
				margin="20px"
				padding="30px"
				border-radius="20"
			>
				<view class="login-form">
					<u-form :model="loginForm" ref="formRef">
						<u-form-item
							:border-bottom="false"
							:error-message="errors.username"
							:show-error="!!errors.username"
						>
							<u-input
								v-model="loginForm.username"
								placeholder="请输入用户名"
								border="surround"
								:clearable="true"
								:error="!!errors.username"
							></u-input>
						</u-form-item>

						<u-gap height="20"></u-gap>

						<u-form-item
							:border-bottom="false"
							:error-message="errors.password"
							:show-error="!!errors.password"
						>
							<u-input
								v-model="loginForm.password"
								type="password"
								placeholder="请输入密码"
								border="surround"
								:clearable="true"
								:error="!!errors.password"
							></u-input>
						</u-form-item>

						<u-gap height="30"></u-gap>

						<u-button
							:text="isLoading ? '登录中...' : '登录'"
							type="primary"
							:loading="isLoading"
							:disabled="isLoading"
							@click="handleLogin"
							shape="circle"
							size="large"
						></u-button>

						<u-gap height="20"></u-gap>

						<view class="register-link">
							<u-text text="还没有账号？" size="14" color="#999"></u-text>
							<u-text
								text="立即注册"
								size="14"
								color="#3c9cff"
								@click="goToRegister"
								:decoration="true"
							></u-text>
						</view>
					</u-form>
				</view>
			</u-card>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script setup>
	import { ref, reactive } from 'vue'
	import AuthManager from '../../utils/auth.uts'

	const loginForm = reactive({
		username: '',
		password: ''
	})

	const errors = reactive({
		username: '',
		password: ''
	})

	const isLoading = ref(false)
	const formRef = ref()

	const validateForm = () => {
		errors.username = ''
		errors.password = ''
		let isValid = true

		if (!loginForm.username.trim()) {
			errors.username = '请输入用户名'
			isValid = false
		} else if (loginForm.username.length < 3) {
			errors.username = '用户名至少3个字符'
			isValid = false
		}

		if (!loginForm.password.trim()) {
			errors.password = '请输入密码'
			isValid = false
		} else if (loginForm.password.length < 6) {
			errors.password = '密码至少6个字符'
			isValid = false
		}

		return isValid
	}

	const handleLogin = async () => {
		if (!validateForm()) {
			return
		}

		isLoading.value = true

		try {
			// 模拟登录API调用
			await simulateLogin(loginForm.username, loginForm.password)

			// 登录成功，保存token
			const token = generateToken()
			AuthManager.saveToken(token)

			uni.showToast({
				title: '登录成功',
				icon: 'success'
			})

			// 跳转到主页
			setTimeout(() => {
				uni.switchTab({
					url: '/pages/index/index'
				})
			}, 1000)

		} catch (error) {
			uni.showToast({
				title: (error as Error)?.message || '登录失败',
				icon: 'error'
			})
		} finally {
			isLoading.value = false
		}
	}

	const simulateLogin = (username, password) => {
		return new Promise((resolve, reject) => {
			setTimeout(() => {
				// 简单的模拟验证逻辑
				if (username === 'admin' && password === '123456') {
					resolve({ success: true })
				} else {
					reject(new Error('用户名或密码错误'))
				}
			}, 1500)
		})
	}

	const generateToken = () => {
		return 'token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
	}


	const goToRegister = () => {
		uni.navigateTo({
			url: '/pages/register/register'
		})
	}
</script>

<style>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		flex-direction: column;
		justify-content: center;
		padding: 40px 0;
	}

	.login-header {
		text-align: center;
		margin-bottom: 50px;
		padding: 0 30px;
	}

	.login-form {
		width: 100%;
	}

	.register-link {
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 5px;
	}
</style>
